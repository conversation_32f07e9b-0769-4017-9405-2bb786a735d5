defmodule MqttableWeb.ConnectionsLive do
  use Mq<PERSON>bleWeb, :live_view
  require Logger

  alias Mqttable.ConnectionSets
  alias Mqttable.Settings
  alias Mqttable.MqttClient.Manager, as: MqttClientManager
  alias MqttableWeb.ConnectionSets.Manager, as: Connection<PERSON>etsManager
  alias MqttableWeb.Connections.Manager, as: ConnectionsManager
  alias MqttableWeb.Variables.Manager, as: VariablesManager
  alias MqttableWeb.UI.StateManager
  alias MqttableWeb.Utils.ConnectionHelpers

  @impl true
  def mount(_params, _session, socket) do
    # Get connection sets from the ConnectionSets server
    connection_sets = ConnectionSets.get_all()

    # Subscribe to connection sets updates, MQTT client status updates, and MQTT trace messages
    if connected?(socket) do
      ConnectionSets.subscribe()
      Settings.subscribe()
      Phoenix.PubSub.subscribe(Mqttable.PubSub, "mqtt_clients")
      Mqttable.MqttTelemetryTracing.subscribe()
    end

    # Configure uploads for payload files
    socket =
      allow_upload(socket, :payload_file,
        accept: :any,
        max_entries: 1,
        # 10MB limit
        max_file_size: 10_000_000
      )

    # Initialize subscription modal related assigns
    socket =
      socket
      |> assign(:show_subscription_modal, false)
      |> assign(:pre_selected_client_id, nil)
      |> assign(:edit_mode, false)
      |> assign(:client_id, nil)
      |> assign(:topic, nil)
      |> assign(:qos, 0)
      |> assign(:nl, false)
      |> assign(:rap, false)
      |> assign(:rh, 0)
      |> assign(:sub_id, nil)
      |> assign(:index, nil)

    available_colors = [
      {"blue", "bg-blue-500"},
      {"green", "bg-green-500"},
      {"red", "bg-red-500"},
      {"yellow", "bg-yellow-500"},
      {"purple", "bg-purple-500"}
    ]

    # Initialize a new connection with default values
    default_connection = %{
      name: ConnectionHelpers.generate_random_connection_name(),
      client_id: "mqtt_" <> ConnectionHelpers.generate_random_string(8),
      username: "",
      password: "",
      mqtt_version: "5.0",
      connect_timeout: 45,
      keep_alive: 300,
      clean_start: true,
      session_expiry_interval: 0,
      receive_maximum: nil,
      maximum_packet_size: nil,
      topic_alias_maximum: nil,
      request_response_info: false,
      request_problem_info: false,
      user_properties: [%{key: "", value: ""}],
      will_topic: "",
      will_qos: "0",
      will_retain: false,
      will_payload: "",
      will_payload_format: false,
      will_delay_interval: 0,
      will_message_expiry: 0,
      will_content_type: "",
      will_response_topic: "",
      will_correlation_data: "",
      topics: [],
      # 默认状态为断开连接
      status: "disconnected",
      # 连接时间，初始为 nil
      connection_time: nil
    }

    # Get UI state from the ConnectionSets server
    ui_state = ConnectionSets.get_ui_state()

    # Get expanded_sets from UI state or initialize as an empty map
    expanded_sets = Map.get(ui_state, :expanded_sets, %{})

    # Get active connection set name from UI state
    active_set_name = Map.get(ui_state, :active_connection_set)

    # Find the active connection set by name
    active_connection_set =
      if active_set_name do
        ConnectionHelpers.find_connection_set_by_name(connection_sets, active_set_name)
      else
        nil
      end

    # Load initial trace messages for the active broker (all messages)
    trace_messages =
      if active_connection_set && active_connection_set.name do
        Mqttable.TraceManager.get_messages(active_connection_set.name)
      else
        []
      end

    socket =
      socket
      |> assign(:connection_sets, connection_sets)
      |> assign(:active_connection_set, active_connection_set)
      |> assign(:show_modal, false)
      |> assign(:modal_type, nil)
      |> assign(:edit_var, nil)
      |> assign(:edit_connection_set, nil)
      |> assign(:edit_connection, default_connection)
      |> assign(:available_colors, available_colors)
      |> assign(:uploaded_files, [])
      |> assign(:expanded_sets, expanded_sets)
      # Initialize trace component state with loaded messages using stream
      |> stream(:trace_messages, trace_messages)
      |> assign(:trace_active, false)
      # Initialize send modal form state from ui_state
      |> assign(:send_modal_form_state, load_send_modal_form_state(active_connection_set))
      # Initialize modal states for send message and message detail modals
      |> assign(:show_send_modal, false)
      |> assign(:show_detail_modal, false)
      |> assign(:detail_modal_message, nil)
      |> assign(:payload_view_type, "plaintext")
      # Initialize settings modal state
      |> assign(:show_settings_modal, false)
      # Initialize scheduled message modal state
      |> assign(:show_scheduled_message_modal, false)
      |> assign(:scheduled_message_edit_mode, false)
      |> assign(:scheduled_message_edit_index, nil)
      |> assign(:scheduled_message_data, nil)
      # Initialize template manager modal state
      |> assign(:show_template_manager, false)

    {:ok, socket}
  end

  @impl true
  def handle_params(_params, _uri, socket) do
    # Since we no longer use URL parameters to track the active broker,
    # we just return the socket without making any changes
    {:noreply, socket}
  end

  @impl true
  def handle_event("open_connection_set_modal", %{"type" => "new_connection_set"}, socket) do
    ConnectionSetsManager.handle_open_connection_set_modal(socket)
  end

  @impl true
  def handle_event("open_edit_connection_set_modal", %{"name" => name}, socket) do
    ConnectionSetsManager.handle_open_edit_connection_set_modal(socket, %{"name" => name})
  end

  @impl true
  def handle_event("open_connections_modal", _params, socket) do
    StateManager.handle_open_connections_modal(socket)
  end

  @impl true
  def handle_event("select_broker_tab", %{"name" => name}, socket) do
    # Find the connection set by name
    set = ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, name)

    if set do
      # Mark this change as initiated locally to prevent loops
      socket = assign(socket, :active_set_change_source, "local")

      # Update the active connection set in the UI state to broadcast to all clients
      ConnectionSets.update_active_connection_set(name)

      # Load trace messages for this broker
      trace_messages = Mqttable.TraceManager.get_messages(name)

      # Load send modal form state for this broker
      send_modal_form_state = load_send_modal_form_state(set)

      {:noreply,
       socket
       |> assign(:active_connection_set, set)
       |> stream(:trace_messages, trace_messages)
       |> assign(:send_modal_form_state, send_modal_form_state)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("close_broker_tab", %{"name" => name}, socket) do
    handle_close_broker_tab(socket, name)
  end

  @impl true
  def handle_event(
        "reorder_broker_tabs",
        %{"old_index" => old_index, "new_index" => new_index},
        socket
      ) do
    # Reorder the connection sets based on the new order
    connection_sets = socket.assigns.connection_sets

    # Move the item from old_index to new_index
    {item, remaining} = List.pop_at(connection_sets, old_index)
    reordered_sets = List.insert_at(remaining, new_index, item)

    # Update the connection sets
    ConnectionSets.update(reordered_sets)

    socket = assign(socket, :connection_sets, reordered_sets)
    {:noreply, socket}
  end

  @impl true
  def handle_event("open_new_connection_modal", %{"name" => set_name}, socket) do
    ConnectionsManager.handle_open_new_connection_modal(socket, %{"name" => set_name})
  end

  @impl true
  def handle_event(
        "open_edit_connection_modal",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    ConnectionsManager.handle_open_edit_connection_modal(
      socket,
      %{"set_name" => set_name, "client_id" => client_id}
    )
  end

  @impl true
  def handle_event("close_modal", _params, socket) do
    StateManager.handle_close_modal(socket)
  end

  @impl true
  def handle_event("open_subscription_modal", %{"set_name" => set_name}, socket) do
    # Find the connection set by name
    active_set =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if active_set do
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_subscription_modal, true)
        |> assign(:pre_selected_client_id, nil)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event(
        "open_subscription_modal_for_client",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    # Find the connection set by name
    active_set =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if active_set do
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_subscription_modal, true)
        |> assign(:pre_selected_client_id, client_id)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("edit_subscription", params, socket) do
    client_id = params["client_id"]
    topic = params["topic"]
    qos = params["qos"]
    nl = params["nl"]
    rap = params["rap"]
    rh = params["rh"]
    sub_id = params["sub_id"]
    index = params["index"]
    # Find the connection set by client_id
    connection_sets = socket.assigns.connection_sets
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil do
      # Get the connection set
      active_set = Enum.at(connection_sets, set_index)

      # Get the connection and find the topic entry to get the subscription identifier
      connection =
        if connection_index != nil do
          Enum.at(active_set.connections, connection_index)
        else
          nil
        end

      # Find the topic entry using the provided index or by searching for the topic
      {topic_entry, topic_index} =
        if connection && connection.topics do
          topics = connection.topics

          # If index is provided and valid, use it directly
          if index != nil && index != "" do
            case Integer.parse(index) do
              {idx, _} when idx >= 0 and idx < length(topics) ->
                {Enum.at(topics, idx), idx}

              _ ->
                # Invalid index, fall back to finding by topic
                idx =
                  Enum.find_index(topics, fn
                    %{topic: t} -> t == topic
                    _ -> false
                  end)

                if idx != nil do
                  {Enum.at(topics, idx), idx}
                else
                  {nil, nil}
                end
            end
          else
            # No index provided, find by topic
            idx =
              Enum.find_index(topics, fn
                %{topic: t} -> t == topic
                _ -> false
              end)

            if idx != nil do
              {Enum.at(topics, idx), idx}
            else
              {nil, nil}
            end
          end
        else
          {nil, nil}
        end

      # Get subscription identifier from topic entry if available
      sub_id_value =
        cond do
          # If sub_id is provided in params, use it
          sub_id && sub_id != "" ->
            sub_id

          # If topic entry has id field, use it
          topic_entry && is_map(topic_entry) && Map.has_key?(topic_entry, :id) ->
            id = Map.get(topic_entry, :id)
            if is_integer(id) && id > 0, do: Integer.to_string(id), else: ""

          # Otherwise, use empty string
          true ->
            ""
        end

      # Convert string values to appropriate types
      qos_int =
        case qos do
          qos when is_binary(qos) -> String.to_integer(qos)
          qos when is_integer(qos) -> qos
          _ -> 0
        end

      # 保持 nl 和 rap 的原始值，不转换为布尔值
      nl_bool =
        case nl do
          "true" -> 1
          true -> 1
          1 -> 1
          "1" -> 1
          _ -> 0
        end

      rap_bool =
        case rap do
          "true" -> 1
          true -> 1
          1 -> 1
          "1" -> 1
          _ -> 0
        end

      rh_int =
        case rh do
          rh when is_binary(rh) -> String.to_integer(rh)
          rh when is_integer(rh) -> rh
          _ -> 0
        end

      # Open the subscription modal with pre-filled values for editing
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_subscription_modal, true)
        |> assign(:edit_mode, true)
        |> assign(:client_id, client_id)
        |> assign(:topic, topic)
        |> assign(:qos, qos_int)
        |> assign(:nl, nl_bool)
        |> assign(:rap, rap_bool)
        |> assign(:rh, rh_int)
        |> assign(:sub_id, sub_id_value)
        |> assign(:index, if(topic_index != nil, do: Integer.to_string(topic_index), else: ""))

      {:noreply, socket}
    else
      # Connection set not found
      socket = put_flash(socket, :error, "Connection not found")
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("unsubscribe_topic", %{"client_id" => client_id, "topic" => topic}, socket) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Check client connection status
      client_status = Mqttable.MqttClient.Manager.get_status(client_id)

      case client_status do
        :connected ->
          # Client is connected, attempt MQTT unsubscribe
          case Mqttable.MqttClient.Manager.unsubscribe(client_id, topic) do
            {:ok, _props, _reason_codes} ->
              # Update the connection's topics list
              {updated_connection_sets, success_message} =
                remove_topic_from_connection_state(
                  connection_sets,
                  set_index,
                  connection_index,
                  connection,
                  topic
                )

              # Update the connection sets in the state
              ConnectionSets.update(updated_connection_sets)

              # Show success message
              socket = put_flash(socket, :info, success_message)
              {:noreply, socket}

            {:error, _reason, error_message} ->
              # Show error message
              socket = put_flash(socket, :error, "Failed to unsubscribe: #{error_message}")
              {:noreply, socket}
          end

        _ ->
          # Client is not connected (:disconnected or :reconnecting)
          # Directly remove topic from state without MQTT unsubscribe
          {updated_connection_sets, success_message} =
            remove_topic_from_connection_state(
              connection_sets,
              set_index,
              connection_index,
              connection,
              topic
            )

          # Update the connection sets in the state
          ConnectionSets.update(updated_connection_sets)

          # Show success message
          socket = put_flash(socket, :info, success_message)
          {:noreply, socket}
      end
    else
      # Connection not found
      socket = put_flash(socket, :error, "Connection not found")
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("generate_client_id", _params, socket) do
    ConnectionsManager.handle_generate_client_id(socket)
  end

  @impl true
  def handle_event("generate_connection_name", _params, socket) do
    ConnectionsManager.handle_generate_connection_name(socket)
  end

  @impl true
  def handle_event("add_user_property", _params, socket) do
    ConnectionsManager.handle_add_user_property(socket)
  end

  @impl true
  def handle_event("remove_user_property", %{"index" => index}, socket) do
    ConnectionsManager.handle_remove_user_property(socket, %{"index" => index})
  end

  @impl true
  def handle_event("user_property_changed", params, socket) do
    ConnectionsManager.handle_user_property_changed(socket, params)
  end

  @impl true
  def handle_event("check_new_property", %{"value" => value}, socket) do
    ConnectionsManager.handle_check_new_property(socket, %{"value" => value})
  end

  @impl true
  def handle_event("save_connection", params, socket) do
    ConnectionsManager.handle_save_connection(socket, params)
  end

  @impl true
  def handle_event("save_and_connect_connection", params, socket) do
    ConnectionsManager.handle_save_and_connect_connection(socket, params)
  end

  @impl true
  def handle_event("update_connection", params, socket) do
    ConnectionsManager.handle_update_connection(socket, params)
  end

  @impl true
  def handle_event(
        "delete_connection",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    ConnectionsManager.handle_delete_connection(
      socket,
      %{"set_name" => set_name, "client_id" => client_id}
    )
  end

  @impl true
  def handle_event(
        "save_connection_set",
        %{"connection_set" => set_params} = params,
        socket
      ) do
    ConnectionSetsManager.handle_save_connection_set(socket, %{
      "connection_set" => set_params,
      "variable" => Map.get(params, "variable", %{})
    })
  end

  @impl true
  def handle_event(
        "update_connection_set",
        %{"connection_set" => set_params, "old_name" => old_name, "variable" => var_params},
        socket
      ) do
    ConnectionSetsManager.handle_update_connection_set(
      socket,
      %{
        "connection_set" => set_params,
        "old_name" => old_name,
        "variable" => var_params
      }
    )
  end

  @impl true
  def handle_event(
        "update_connection_set",
        %{"connection_set" => set_params, "old_name" => old_name},
        socket
      ) do
    ConnectionSetsManager.handle_update_connection_set(
      socket,
      %{
        "connection_set" => set_params,
        "old_name" => old_name
      }
    )
  end

  @impl true
  def handle_event("save_variable", %{"variable" => var_params}, socket) do
    VariablesManager.handle_save_variable(socket, %{"variable" => var_params})
  end

  @impl true
  def handle_event("update_variable", %{"variable" => var_params, "old_name" => old_name}, socket) do
    VariablesManager.handle_update_variable(socket, %{
      "variable" => var_params,
      "old_name" => old_name
    })
  end

  @impl true
  def handle_event("delete_variable", %{"name" => name}, socket) do
    VariablesManager.handle_delete_variable(socket, %{"name" => name})
  end

  @impl true
  def handle_event("delete_connection_set", %{"name" => name}, socket) do
    ConnectionSetsManager.handle_delete_connection_set(socket, %{"name" => name})
  end

  @impl true
  def handle_event(
        "update_connection_status",
        %{"client_id" => client_id, "new_status" => new_status},
        socket
      ) do
    # Get the current active connection set
    active_set = socket.assigns.active_connection_set

    if active_set do
      # Find the connection to update
      connections = Map.get(active_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        # Handle MQTT connection based on the new status
        connection_result =
          try do
            case new_status do
              "connected" ->
                # Attempt to establish MQTT connection
                case MqttClientManager.connect(connection, active_set) do
                  {:ok, _} ->
                    {:ok, new_status}

                  {:error, reason, error_message} ->
                    # If connection fails, set status to reconnecting and store the error message
                    Logger.error(
                      "Failed to connect MQTT client #{client_id}: #{inspect(reason)} - #{error_message}"
                    )

                    {:error, "reconnecting", error_message}
                end

              "disconnected" ->
                # Disconnect MQTT client if it was connected
                # Always call disconnect, our improved implementation handles the case
                # when the client is already disconnected
                MqttClientManager.disconnect(client_id)

                # Always return success for disconnection
                {:ok, new_status}

              _ ->
                # For other statuses, just update the status
                {:ok, new_status}
            end
          catch
            :exit, {:shutdown, :tcp_closed} ->
              error_message = "Connection closed by broker before completing handshake"
              Logger.error("MQTT client #{client_id}: #{error_message}")
              {:error, "reconnecting", error_message}

            :exit, {:socket_closed_before_connack, _} ->
              error_message = "Connection closed by broker before completing handshake"
              Logger.error("MQTT client #{client_id}: #{error_message}")
              {:error, "reconnecting", error_message}

            :exit, reason ->
              # Extract just the essential error information without all the parameters
              error_message = extract_concise_error_message(reason)
              Logger.error("MQTT client #{client_id}: #{error_message}")
              {:error, "reconnecting", error_message}
          end

        # Update connection status based on the result
        {status, actual_status, error_message} =
          case connection_result do
            {:ok, status} ->
              {:ok, status, nil}

            {:error, status, message} ->
              {:error, status, message}
          end

        # Update the connections list
        updated_connections =
          Enum.map(connections, fn conn ->
            if conn.client_id == client_id do
              # If the status is changing to connected, add a timestamp
              # If the status is changing to disconnected, clear the connection_time
              cond do
                actual_status == "connected" ->
                  conn
                  |> Map.put(:status, actual_status)
                  |> Map.put(:connection_time, DateTime.utc_now())

                actual_status == "disconnected" ->
                  conn
                  |> Map.put(:status, actual_status)
                  |> Map.put(:connection_time, nil)

                true ->
                  Map.put(conn, :status, actual_status)
              end
            else
              conn
            end
          end)

        # Update the active connection set
        updated_set = Map.put(active_set, :connections, updated_connections)

        # Get all connection sets
        connection_sets = socket.assigns.connection_sets

        # Update the connection sets list
        updated_connection_sets =
          Enum.map(connection_sets, fn set ->
            if set.name == active_set.name do
              updated_set
            else
              set
            end
          end)

        # Update the connection sets in the server
        Mqttable.ConnectionSets.update(updated_connection_sets)

        # Update socket assigns
        socket =
          assign(socket,
            active_connection_set: updated_set,
            connection_sets: updated_connection_sets
          )

        # Note: No need to manually broadcast, Mqttable.ConnectionSets.update already broadcasts connection_sets_updated event

        # If there was an error, show a notification with the specific error message
        socket =
          if status == :error do
            # Use the specific error message if available
            flash_message =
              if error_message,
                do: error_message,
                else: "Failed to connect to MQTT broker. Retrying..."

            put_flash(socket, :error, flash_message)
          else
            socket
          end

        {:noreply, socket}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("validate", _params, socket) do
    {:noreply, socket}
  end

  @impl true
  def handle_event("cancel-upload", %{"upload" => upload_name, "ref" => ref}, socket) do
    {:noreply, cancel_upload(socket, String.to_existing_atom(upload_name), ref)}
  end

  @impl true
  def handle_event("cancel-upload", %{"ref" => ref}, socket) do
    # Handle payload file upload cancellation
    {:noreply, cancel_upload(socket, :payload_file, ref)}
  end

  @impl true
  def handle_event("clear_variables", _params, socket) do
    VariablesManager.handle_clear_variables(socket)
  end

  @impl true
  def handle_event("add_variable_row", params, socket) do
    VariablesManager.handle_add_variable_row(socket, params)
  end

  @impl true
  def handle_event(
        "mqtt_version_changed",
        %{"connection" => %{"mqtt_version" => _mqtt_version}} = params,
        socket
      ) do
    ConnectionsManager.handle_mqtt_version_changed(socket, params)
  end

  @impl true
  def handle_event("open_send_modal", _params, socket) do
    # Load the latest form state for the current broker when opening the modal
    active_connection_set = socket.assigns[:active_connection_set]
    send_modal_form_state = load_send_modal_form_state(active_connection_set)

    socket =
      socket
      |> assign(:show_send_modal, true)
      |> assign(:send_modal_form_state, send_modal_form_state)

    {:noreply, socket}
  end

  @impl true
  def handle_event("close_send_modal", _params, socket) do
    {:noreply, assign(socket, :show_send_modal, false)}
  end

  @impl true
  def handle_event("open_detail_modal", %{"message" => message}, socket) do
    socket =
      socket
      |> assign(:show_detail_modal, true)
      |> assign(:detail_modal_message, message)

    {:noreply, socket}
  end

  @impl true
  def handle_event("close_detail_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_detail_modal, false)
      |> assign(:detail_modal_message, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event("open_settings_modal", _params, socket) do
    {:noreply, assign(socket, :show_settings_modal, true)}
  end

  @impl true
  def handle_event("close_settings_modal", _params, socket) do
    {:noreply, assign(socket, :show_settings_modal, false)}
  end

  @impl true
  def handle_event(
        "open_scheduled_message_modal_for_client",
        %{"set_name" => set_name, "client_id" => client_id},
        socket
      ) do
    # Find the connection set by name
    active_set =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, set_name)

    if active_set do
      socket =
        socket
        |> assign(:active_connection_set, active_set)
        |> assign(:show_scheduled_message_modal, true)
        |> assign(:pre_selected_client_id, client_id)
        |> assign(:scheduled_message_edit_mode, false)
        |> assign(:scheduled_message_edit_index, nil)
        |> assign(:scheduled_message_data, nil)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("close_scheduled_message_modal", _params, socket) do
    socket =
      socket
      |> assign(:show_scheduled_message_modal, false)
      |> assign(:pre_selected_client_id, nil)
      |> assign(:scheduled_message_edit_mode, false)
      |> assign(:scheduled_message_edit_index, nil)
      |> assign(:scheduled_message_data, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_event(
        "edit_scheduled_message",
        %{"client_id" => client_id, "index" => index_str},
        socket
      ) do
    index = String.to_integer(index_str)

    # Find the connection and scheduled message
    case find_connection_and_scheduled_message(socket.assigns.connection_sets, client_id, index) do
      {connection_set, _connection, scheduled_message} ->
        socket =
          socket
          |> assign(:active_connection_set, connection_set)
          |> assign(:show_scheduled_message_modal, true)
          |> assign(:pre_selected_client_id, client_id)
          |> assign(:scheduled_message_edit_mode, true)
          |> assign(:scheduled_message_edit_index, index)
          |> assign(:scheduled_message_data, scheduled_message)

        {:noreply, socket}

      nil ->
        {:noreply, put_flash(socket, :error, "Scheduled message not found")}
    end
  end

  @impl true
  def handle_event(
        "remove_scheduled_message",
        %{"client_id" => client_id, "index" => index_str},
        socket
      ) do
    index = String.to_integer(index_str)

    # Find and remove the scheduled message
    updated_connection_sets =
      remove_scheduled_message_from_sets(socket.assigns.connection_sets, client_id, index)

    if updated_connection_sets != socket.assigns.connection_sets do
      # Update the connection sets in the server
      Mqttable.ConnectionSets.update(updated_connection_sets)

      # Update socket assigns first
      socket =
        assign(socket,
          connection_sets: updated_connection_sets,
          active_connection_set:
            ConnectionHelpers.find_connection_set_by_name(
              updated_connection_sets,
              socket.assigns.active_connection_set.name
            )
        )

      # Synchronize all scheduled messages with the worker
      sync_client_scheduled_messages(socket, client_id)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_event("switch_payload_view", %{"type" => type}, socket) do
    # Switch payload view type between plaintext, json, and hex
    valid_types = ["plaintext", "json", "hex"]
    payload_view_type = if type in valid_types, do: type, else: "plaintext"
    {:noreply, assign(socket, :payload_view_type, payload_view_type)}
  end

  @impl true
  def handle_event("load_older_messages", _params, socket) do
    # This event should be handled by TraceGridComponent, but sometimes
    # it gets sent to the parent LiveView. We just ignore it here.
    {:noreply, socket}
  end

  @impl true
  def handle_event("load_newer_messages", _params, socket) do
    # This event should be handled by TraceGridComponent, but sometimes
    # it gets sent to the parent LiveView. We just ignore it here.
    {:noreply, socket}
  end

  @impl true
  def handle_info({:close_broker_tab, name}, socket) do
    # Handle the close broker tab message from the component
    handle_close_broker_tab(socket, name)
  end

  @impl true
  def handle_info({:open_edit_connection_set_modal, params}, socket) do
    # Call the existing handler with the params
    handle_event("open_edit_connection_set_modal", params, socket)
  end

  @impl true
  def handle_info({:focus_element, element_id}, socket) do
    StateManager.handle_focus_element(socket, element_id)
  end

  @impl true
  def handle_info({:close_settings_modal}, socket) do
    {:noreply, assign(socket, :show_settings_modal, false)}
  end

  @impl true
  def handle_info({:settings_saved_redirect}, socket) do
    # Close the settings modal and redirect to refresh the page
    socket =
      socket
      |> assign(:show_settings_modal, false)
      |> redirect(to: ~p"/")

    {:noreply, socket}
  end

  @impl true
  def handle_info({:settings_updated, _updated_settings}, socket) do
    # Settings have been updated, no need to update socket state
    # The components will use the new settings automatically
    {:noreply, socket}
  end

  @impl true
  def handle_info({:connection_sets_updated, updated_connection_sets}, socket) do
    StateManager.handle_connection_sets_updated(socket, updated_connection_sets)
  end

  @impl true
  def handle_info({:ui_state_updated, updated_ui_state}, socket) do
    StateManager.handle_ui_state_updated(socket, updated_ui_state)
  end

  @impl true
  def handle_info({:save_connection_set, params}, socket) do
    # Call the existing handler with the params
    handle_event("save_connection_set", params, socket)
  end

  @impl true
  def handle_info({:update_connection_set, params}, socket) do
    # Call the existing handler with the params
    handle_event("update_connection_set", params, socket)
  end

  @impl true
  def handle_info({:delete_connection_set, params}, socket) do
    # Call the existing handler with the params
    handle_event("delete_connection_set", params, socket)
  end

  @impl true
  def handle_info({:save_variable, params}, socket) do
    # Call the existing handler with the params
    handle_event("save_variable", params, socket)
  end

  @impl true
  def handle_info({:update_variable, params}, socket) do
    # Call the existing handler with the params
    handle_event("update_variable", params, socket)
  end

  @impl true
  def handle_info({:save_connection, connection_params}, socket) do
    # Call the existing handler with the params
    handle_event("save_connection", %{"connection" => connection_params}, socket)
  end

  @impl true
  def handle_info({:save_and_connect_connection, connection_params}, socket) do
    # Call the existing handler with the params
    handle_event("save_and_connect_connection", %{"connection" => connection_params}, socket)
  end

  @impl true
  def handle_info({:update_connection, old_client_id, connection_params}, socket) do
    # Call the existing handler with the params (legacy format)
    handle_event(
      "update_connection",
      %{"connection" => connection_params, "old_client_id" => old_client_id},
      socket
    )
  end

  @impl true
  def handle_info({:update_connection_with_full_params, params}, socket) do
    # Call the existing handler with the complete params (including user properties)
    handle_event("update_connection", params, socket)
  end

  @impl true
  def handle_info({:user_property_changed, params}, socket) do
    # Call the existing handler with the params
    handle_event("user_property_changed", params, socket)
  end

  @impl true
  def handle_info({:add_user_property}, socket) do
    # Call the existing handler
    handle_event("add_user_property", %{}, socket)
  end

  @impl true
  def handle_info({:remove_user_property, params}, socket) do
    # Call the existing handler with the params
    handle_event("remove_user_property", params, socket)
  end

  @impl true
  def handle_info({:mqtt_client_status_changed, client_id, status}, socket) do
    # Get the current active connection set
    active_set = socket.assigns.active_connection_set

    if active_set do
      # Find the connection to update
      connections = Map.get(active_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        # Convert atom status to string
        string_status = Atom.to_string(status)

        # Only update if the status has changed
        if connection.status != string_status do
          # Update the connections list
          updated_connections =
            Enum.map(connections, fn conn ->
              if conn.client_id == client_id do
                # If status is disconnected, clear the connection_time
                if string_status == "disconnected" do
                  conn
                  |> Map.put(:status, string_status)
                  |> Map.put(:connection_time, nil)
                else
                  Map.put(conn, :status, string_status)
                end
              else
                conn
              end
            end)

          # Update the active connection set
          updated_set = Map.put(active_set, :connections, updated_connections)

          # Get all connection sets
          connection_sets = socket.assigns.connection_sets

          # Update the connection sets list
          updated_connection_sets =
            Enum.map(connection_sets, fn set ->
              if set.name == active_set.name do
                updated_set
              else
                set
              end
            end)

          # Update the connection sets in the server
          Mqttable.ConnectionSets.update(updated_connection_sets)

          # Update socket assigns
          socket =
            assign(socket,
              active_connection_set: updated_set,
              connection_sets: updated_connection_sets
            )

          {:noreply, socket}
        else
          {:noreply, socket}
        end
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_client_connection_error, client_id, error_message}, socket) do
    # Get the current active connection set
    active_set = socket.assigns.active_connection_set

    if active_set do
      # Find the connection that had the error
      connections = Map.get(active_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        # Display a concise flash message with the error details
        socket =
          put_flash(socket, :error, "Connection failed for #{client_id}: #{error_message}")

        {:noreply, socket}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_client_connection_time, client_id, timestamp}, socket) do
    # Get the current active connection set
    active_set = socket.assigns.active_connection_set

    if active_set do
      # Find the connection to update
      connections = Map.get(active_set, :connections, [])
      connection = Enum.find(connections, fn conn -> conn.client_id == client_id end)

      if connection do
        # Update the connection_time
        updated_connections =
          Enum.map(connections, fn conn ->
            if conn.client_id == client_id do
              Map.put(conn, :connection_time, timestamp)
            else
              conn
            end
          end)

        # Update the active connection set
        updated_set = Map.put(active_set, :connections, updated_connections)

        # Get all connection sets
        connection_sets = socket.assigns.connection_sets

        # Update the connection sets list
        updated_connection_sets =
          Enum.map(connection_sets, fn set ->
            if set.name == active_set.name do
              updated_set
            else
              set
            end
          end)

        # Update the connection sets in the server
        Mqttable.ConnectionSets.update(updated_connection_sets)

        # Update socket assigns
        socket =
          assign(socket,
            active_connection_set: updated_set,
            connection_sets: updated_connection_sets
          )

        {:noreply, socket}
      else
        {:noreply, socket}
      end
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:close_subscription_modal}, socket) do
    socket =
      socket
      |> assign(:show_subscription_modal, false)
      |> assign(:edit_mode, false)
      |> assign(:client_id, nil)
      |> assign(:topic, nil)
      |> assign(:qos, 0)
      |> assign(:nl, false)
      |> assign(:rap, false)
      |> assign(:rh, 0)
      |> assign(:sub_id, nil)
      |> assign(:index, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:subscribe_to_topic, client_id, topic, sub_opts, sub_id, index}, socket) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Prepare options for subscription
      opts = [sub_opts: sub_opts]

      # Add subscription identifier if provided
      opts = if sub_id, do: Keyword.put(opts, :id, sub_id), else: opts

      # Attempt to subscribe to the topic
      case Mqttable.MqttClient.Manager.subscribe(client_id, topic, opts) do
        {:ok, _props, _reason_codes} ->
          # Update the connection's topics list with topic and options
          # 保持 nl 和 rap 的原始值，不转换为布尔值
          nl_value = Keyword.get(sub_opts, :nl, 0)
          rap_value = Keyword.get(sub_opts, :rap, 0)

          # Get subscription identifier from the opts parameter, not from sub_opts
          sub_id_value = sub_id

          # Create topic entry with all subscription options
          topic_entry = %{
            topic: topic,
            qos: Keyword.get(sub_opts, :qos, 0),
            nl: nl_value,
            rap: rap_value,
            rh: Keyword.get(sub_opts, :rh, 0)
          }

          # Add subscription identifier if provided
          topic_entry =
            if sub_id_value && is_integer(sub_id_value) && sub_id_value > 0 do
              Map.put(topic_entry, :id, sub_id_value)
            else
              topic_entry
            end

          # Get current topics (always using new format - list of maps)
          current_topics = connection.topics || []

          # Check if we're editing an existing topic or adding a new one
          # Use the index parameter to determine if this is an edit or a new subscription
          updated_topics =
            if index != nil && index != "" do
              # Try to convert index to integer
              case Integer.parse(index) do
                {idx, _} when idx >= 0 and idx < length(current_topics) ->
                  # Update existing topic at the specified index
                  List.replace_at(current_topics, idx, topic_entry)

                _ ->
                  # Invalid index, add as new topic
                  current_topics ++ [topic_entry]
              end
            else
              # No index provided, add as new topic
              current_topics ++ [topic_entry]
            end

          updated_connection = Map.put(connection, :topics, updated_topics)

          # Update the connection in the connection set
          updated_connections =
            List.replace_at(set.connections, connection_index, updated_connection)

          updated_set = Map.put(set, :connections, updated_connections)

          # Update the connection set in the connection sets list
          updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

          # Update the connection sets in the state
          ConnectionSets.update(updated_connection_sets)

          # No flash message for successful subscription (similar to schedule message behavior)
          {:noreply, socket}

        {:error, _reason, error_message} ->
          # Show error message
          socket = put_flash(socket, :error, "Failed to subscribe: #{error_message}")
          {:noreply, socket}

        {:error, :not_connected} ->
          # Show error message
          socket = put_flash(socket, :error, "Client is not connected")
          {:noreply, socket}
      end
    else
      # Connection not found
      socket = put_flash(socket, :error, "Connection not found")
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_client_topic_subscribed, client_id, topic, opts, sub_id}, socket) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Check if the topic is already in the list
      current_topics = connection.topics || []

      # Check if the topic exists in the list (always using new format - list of maps)
      topic_index =
        Enum.find_index(current_topics, fn t ->
          case t do
            %{topic: topic_str} -> topic_str == topic
            _ -> false
          end
        end)

      topic_exists = topic_index != nil

      if topic_exists do
        # Topic already exists, no need to update
        {:noreply, socket}
      else
        # Update the connection's topics list with topic and options
        # 保持 nl 和 rap 的原始值，不转换为布尔值
        nl_value = Keyword.get(opts, :nl, 0)
        rap_value = Keyword.get(opts, :rap, 0)

        # Get subscription identifier from the sub_id parameter
        sub_id_value = sub_id

        # Create topic entry with all subscription options
        topic_entry = %{
          topic: topic,
          qos: Keyword.get(opts, :qos, 0),
          nl: nl_value,
          rap: rap_value,
          rh: Keyword.get(opts, :rh, 0)
        }

        # Add subscription identifier if provided
        topic_entry =
          if sub_id_value && is_integer(sub_id_value) && sub_id_value > 0 do
            Map.put(topic_entry, :id, sub_id_value)
          else
            topic_entry
          end

        # Get current topics (always using new format - list of maps)
        current_topics = connection.topics || []

        # Add new topic
        updated_topics = current_topics ++ [topic_entry]

        updated_connection = Map.put(connection, :topics, updated_topics)

        # Update the connection in the connection set
        updated_connections =
          List.replace_at(set.connections, connection_index, updated_connection)

        updated_set = Map.put(set, :connections, updated_connections)

        # Update the connection set in the connection sets list
        updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

        # Update the connection sets in the state
        ConnectionSets.update(updated_connection_sets)

        # Update socket assigns
        socket =
          assign(socket,
            active_connection_set: updated_set,
            connection_sets: updated_connection_sets
          )

        {:noreply, socket}
      end
    else
      # Connection not found
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_client_topic_unsubscribed, client_id, topic}, socket) do
    # Find the connection set and connection
    connection_sets = socket.assigns.connection_sets

    # Find the connection set containing the client_id
    {set_index, connection_index} = find_connection_indices(connection_sets, client_id)

    if set_index != nil and connection_index != nil do
      # Get the connection set and connection
      set = Enum.at(connection_sets, set_index)
      connection = Enum.at(set.connections, connection_index)

      # Update the connection's topics list
      current_topics = connection.topics || []

      # Remove the topic from the list (always using new format - list of maps)
      updated_topics =
        Enum.reject(current_topics, fn t ->
          case t do
            %{topic: topic_str} -> topic_str == topic
            _ -> false
          end
        end)

      updated_connection = Map.put(connection, :topics, updated_topics)

      # Update the connection in the connection set
      updated_connections = List.replace_at(set.connections, connection_index, updated_connection)
      updated_set = Map.put(set, :connections, updated_connections)

      # Update the connection set in the connection sets list
      updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

      # Update the connection sets in the state
      ConnectionSets.update(updated_connection_sets)

      # Update socket assigns
      socket =
        assign(socket,
          active_connection_set: updated_set,
          connection_sets: updated_connection_sets
        )

      {:noreply, socket}
    else
      # Connection not found
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:mqtt_trace_message, trace_message}, socket) do
    # If settings modal is open, skip processing to avoid disrupting user interaction
    if socket.assigns[:show_settings_modal] do
      {:noreply, socket}
    else
      # Only add the trace message if it belongs to the currently active broker
      active_broker_name =
        if socket.assigns.active_connection_set do
          socket.assigns.active_connection_set.name
        else
          nil
        end

      # Check if this trace message belongs to the active broker
      should_add_message =
        if active_broker_name do
          # Find which broker this client_id belongs to
          case find_broker_by_client_id(trace_message.client_id, socket.assigns.connection_sets) do
            {:ok, broker_name} -> broker_name == active_broker_name
            {:error, _} -> false
          end
        else
          false
        end

      if should_add_message do
        # Add the new trace message to the stream at the beginning (newest first)
        # The TraceGridComponent will handle adding it to the grid
        # Note: stream_insert with at: 0 adds to the beginning
        {:noreply, stream_insert(socket, :trace_messages, trace_message, at: 0)}
      else
        # Message doesn't belong to active broker, ignore it
        {:noreply, socket}
      end
    end
  end

  @impl true
  def handle_info({:clear_trace_messages, broker_name}, socket) do
    # Clear the trace_messages state for the specified broker
    # This is called when the TraceGridComponent clears its messages
    active_broker_name =
      if socket.assigns.active_connection_set do
        socket.assigns.active_connection_set.name
      else
        nil
      end

    # Only clear if the broker matches the currently active broker
    if broker_name == active_broker_name do
      {:noreply, stream(socket, :trace_messages, [])}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:message_sent_successfully, packet_info}, socket) do
    {message, flash_type} = format_publish_result(packet_info)

    # Show flash message for all cases including successful sends
    socket =
      case flash_type do
        :error ->
          put_flash(socket, :error, message)

        :warning ->
          put_flash(socket, :warning, message)

        :info ->
          # Success case - show success flash message with auto-dismiss
          socket
          |> put_flash(:info, message)
          |> Phoenix.LiveView.push_event("auto_dismiss_flash", %{kind: "info", delay: 5000})
      end

    {:noreply, socket}
  end

  @impl true
  def handle_info({:message_send_error, error_message}, socket) do
    socket = put_flash(socket, :error, error_message)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:update_send_modal_form, form_state}, socket) do
    # Save form state to ui_state for the current broker
    active_connection_set = socket.assigns[:active_connection_set]
    broker_name = if active_connection_set, do: active_connection_set.name, else: nil

    if broker_name do
      Mqttable.ConnectionSets.update_send_modal_form_state(broker_name, form_state)
    end

    # Only update socket if form state actually changed to prevent unnecessary re-renders
    current_form_state = socket.assigns[:send_modal_form_state]

    if current_form_state != form_state do
      {:noreply, assign(socket, :send_modal_form_state, form_state)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:update_send_modal_form, form_state, broker_name}, socket) do
    # Save form state to ui_state for the specified broker
    if broker_name do
      Mqttable.ConnectionSets.update_send_modal_form_state(broker_name, form_state)
    end

    # Only update socket if form state actually changed to prevent unnecessary re-renders
    current_form_state = socket.assigns[:send_modal_form_state]

    if current_form_state != form_state do
      {:noreply, assign(socket, :send_modal_form_state, form_state)}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:open_detail_modal, message}, socket) do
    socket =
      socket
      |> assign(:show_detail_modal, true)
      |> assign(:detail_modal_message, message)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:close_scheduled_message_modal}, socket) do
    socket =
      socket
      |> assign(:show_scheduled_message_modal, false)
      |> assign(:pre_selected_client_id, nil)
      |> assign(:scheduled_message_edit_mode, false)
      |> assign(:scheduled_message_edit_index, nil)
      |> assign(:scheduled_message_data, nil)

    {:noreply, socket}
  end

  @impl true
  def handle_info({:add_scheduled_message, client_id, scheduled_message}, socket) do
    # Add scheduled message to the connection
    updated_connection_sets =
      add_scheduled_message_to_sets(socket.assigns.connection_sets, client_id, scheduled_message)

    if updated_connection_sets != socket.assigns.connection_sets do
      # Update the connection sets in the server
      Mqttable.ConnectionSets.update(updated_connection_sets)

      # Update socket assigns first
      socket =
        assign(socket,
          connection_sets: updated_connection_sets,
          active_connection_set:
            ConnectionHelpers.find_connection_set_by_name(
              updated_connection_sets,
              socket.assigns.active_connection_set.name
            )
        )

      # Synchronize all scheduled messages with the worker
      sync_client_scheduled_messages(socket, client_id)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:update_scheduled_message, client_id, index, scheduled_message}, socket) do
    # Update scheduled message in the connection
    updated_connection_sets =
      update_scheduled_message_in_sets(
        socket.assigns.connection_sets,
        client_id,
        index,
        scheduled_message
      )

    if updated_connection_sets != socket.assigns.connection_sets do
      # Update the connection sets in the server
      Mqttable.ConnectionSets.update(updated_connection_sets)

      # Update socket assigns first
      socket =
        assign(socket,
          connection_sets: updated_connection_sets,
          active_connection_set:
            ConnectionHelpers.find_connection_set_by_name(
              updated_connection_sets,
              socket.assigns.active_connection_set.name
            )
        )

      # Synchronize all scheduled messages with the worker
      sync_client_scheduled_messages(socket, client_id)

      {:noreply, socket}
    else
      {:noreply, socket}
    end
  end

  @impl true
  def handle_info({:push_grid_data_update, data}, socket) do
    # Push grid data update event to the TraceSlickGrid JavaScript Hook
    # Use push_event with 3 parameters and target the specific element
    socket = Phoenix.LiveView.push_event(socket, "grid_data_update", data)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:push_export_event, data}, socket) do
    # Push export event to the TraceSlickGrid JavaScript Hook
    socket = Phoenix.LiveView.push_event(socket, "export_data", data)
    {:noreply, socket}
  end

  @impl true
  def handle_info({:broker_tabs_collapsed_changed, _collapsed}, socket) do
    # No need to store state in socket, CSS will read from expanded_sets directly
    {:noreply, socket}
  end

  # Template manager event handlers removed - using simplified payload editor now

  @impl true
  def handle_info({:payload_editor_changed, payload, payload_format}, socket) do
    # Handle payload editor changes from PayloadEditorComponent
    # Update the send modal form state
    current_form_state = socket.assigns[:send_modal_form_state] || %{}

    updated_form_state =
      current_form_state
      |> Map.put("payload", payload)
      |> Map.put("payload_format", payload_format)

    # Save form state to ui_state for the current broker
    active_connection_set = socket.assigns[:active_connection_set]
    broker_name = if active_connection_set, do: active_connection_set.name, else: nil

    if broker_name do
      Mqttable.ConnectionSets.update_send_modal_form_state(broker_name, updated_form_state)
    end

    {:noreply, assign(socket, :send_modal_form_state, updated_form_state)}
  end

  @impl true
  def handle_info({:cancel_payload_upload, ref}, socket) do
    {:noreply, cancel_upload(socket, :payload_file, ref)}
  end

  @impl true
  def handle_info({:process_payload_upload, component_id}, socket) do
    uploaded_files =
      consume_uploaded_entries(socket, :payload_file, fn %{path: path}, entry ->
        # Read file content
        file_content = File.read!(path)

        # Convert to base64
        base64_content = Base.encode64(file_content)

        # Create file info
        file_info = %{
          name: entry.client_name,
          size: entry.client_size,
          type: entry.client_type,
          content: base64_content
        }

        {:ok, file_info}
      end)

    case uploaded_files do
      [file_info] ->
        # Validate file upload
        case validate_file_upload(file_info) do
          {:ok, validated_file} ->
            # Generate preview for images
            file_preview =
              if String.starts_with?(validated_file.type, "image/") do
                "data:#{validated_file.type};base64,#{validated_file.content}"
              else
                nil
              end

            # Send update to the component
            send_update(MqttableWeb.EnhancedPayloadEditorComponent,
              id: component_id,
              uploaded_file: validated_file,
              file_preview: file_preview,
              payload: validated_file.content,
              payload_format: "file",
              file_upload_error: nil
            )

            # Update form state
            current_form_state = socket.assigns[:send_modal_form_state] || %{}

            updated_form_state =
              current_form_state
              |> Map.put("payload", validated_file.content)
              |> Map.put("payload_format", "file")

            # Save form state to ui_state for the current broker
            active_connection_set = socket.assigns[:active_connection_set]
            broker_name = if active_connection_set, do: active_connection_set.name, else: nil

            if broker_name do
              Mqttable.ConnectionSets.update_send_modal_form_state(
                broker_name,
                updated_form_state
              )
            end

            {:noreply, assign(socket, :send_modal_form_state, updated_form_state)}

          {:error, error_message} ->
            # Send error to the component
            send_update(MqttableWeb.EnhancedPayloadEditorComponent,
              id: component_id,
              file_upload_error: error_message,
              uploaded_file: nil,
              file_preview: nil
            )

            {:noreply, socket}
        end

      [] ->
        # Send error to the component
        send_update(MqttableWeb.EnhancedPayloadEditorComponent,
          id: component_id,
          file_upload_error: "No file was uploaded"
        )

        {:noreply, socket}

      _ ->
        # Send error to the component
        send_update(MqttableWeb.EnhancedPayloadEditorComponent,
          id: component_id,
          file_upload_error: "Multiple files not supported"
        )

        {:noreply, socket}
    end
  rescue
    error ->
      require Logger
      Logger.error("File upload error: #{inspect(error)}")

      # Send error to the component
      send_update(MqttableWeb.EnhancedPayloadEditorComponent,
        id: component_id,
        file_upload_error: "Failed to process uploaded file",
        uploaded_file: nil,
        file_preview: nil
      )

      {:noreply, socket}
  end

  # Serialize message for JSON encoding to avoid Protocol.UndefinedError with tuples
  defp serialize_message_for_json(message) when is_map(message) do
    message
    |> Map.update(:topics, [], &serialize_topics_for_json/1)
    |> Map.update(:properties, %{}, &serialize_properties_for_json/1)
    |> ensure_json_serializable()
  end

  defp serialize_message_for_json(message), do: message

  defp serialize_topics_for_json(topics) when is_list(topics) do
    Enum.map(topics, fn
      {topic, opts} when is_binary(topic) and is_map(opts) ->
        %{topic: topic, options: opts}

      topic when is_binary(topic) ->
        %{topic: topic, options: %{}}

      other ->
        %{topic: to_string(other), options: %{}}
    end)
  end

  defp serialize_topics_for_json(topics), do: topics

  defp serialize_properties_for_json(properties) when is_map(properties) do
    # Ensure all property values are JSON-serializable
    Enum.into(properties, %{}, fn {key, value} ->
      {key, ensure_json_value(value)}
    end)
  end

  defp serialize_properties_for_json(properties), do: properties

  defp ensure_json_serializable(data) when is_map(data) do
    Enum.into(data, %{}, fn {key, value} ->
      {key, ensure_json_value(value)}
    end)
  end

  defp ensure_json_serializable(data) when is_list(data) do
    Enum.map(data, &ensure_json_value/1)
  end

  defp ensure_json_serializable(data), do: ensure_json_value(data)

  defp ensure_json_value(value) when is_binary(value) do
    if String.valid?(value) do
      value
    else
      # Encode non-UTF-8 binary data as base64 for safe JSON serialization
      Base.encode64(value)
    end
  end

  defp ensure_json_value(value) when is_number(value) or is_boolean(value) or is_nil(value),
    do: value

  defp ensure_json_value(value) when is_atom(value), do: to_string(value)
  defp ensure_json_value(value) when is_list(value), do: Enum.map(value, &ensure_json_value/1)
  defp ensure_json_value(value) when is_map(value), do: ensure_json_serializable(value)

  defp ensure_json_value(value) when is_tuple(value),
    do: Tuple.to_list(value) |> ensure_json_value()

  defp ensure_json_value(value), do: to_string(value)

  # Helper function to load send modal form state from ui_state
  defp load_send_modal_form_state(active_connection_set) do
    if active_connection_set do
      broker_name = active_connection_set.name
      Mqttable.ConnectionSets.get_send_modal_form_state(broker_name) || default_publish_form()
    else
      default_publish_form()
    end
  end

  # Helper function to find broker name by client_id
  defp find_broker_by_client_id(client_id, connection_sets) do
    # Use the provided connection sets
    connection_sets = connection_sets || []

    # Search through all connection sets to find the one containing this client_id
    result =
      Enum.find_value(connection_sets, fn set ->
        # Check if any connection in this set has the matching client_id
        connection =
          Enum.find(set[:connections] || [], fn conn ->
            Map.get(conn, :client_id) == client_id
          end)

        if connection do
          set[:name]
        else
          nil
        end
      end)

    case result do
      nil -> {:error, :broker_not_found}
      broker_name -> {:ok, broker_name}
    end
  end

  # Helper function to extract a concise error message from complex error reasons
  defp extract_concise_error_message(reason) do
    cond do
      # Handle socket_closed_before_connack errors
      is_tuple(reason) && tuple_size(reason) >= 2 &&
          elem(reason, 0) == :socket_closed_before_connack ->
        "Connection closed by broker before completing handshake"

      # Handle tcp_closed errors
      is_tuple(reason) && tuple_size(reason) >= 2 && elem(reason, 0) == :shutdown &&
          elem(reason, 1) == :tcp_closed ->
        "Connection closed by broker before completing handshake"

      # Handle GenServer call errors with nested reasons
      is_tuple(reason) && tuple_size(reason) >= 2 && is_tuple(elem(reason, 0)) ->
        inner_reason = elem(reason, 0)

        if is_tuple(inner_reason) && tuple_size(inner_reason) >= 2 do
          case elem(inner_reason, 0) do
            :shutdown ->
              case elem(inner_reason, 1) do
                :tcp_closed -> "Connection closed by broker before completing handshake"
                other -> "Connection error: #{inspect(other)}"
              end

            other ->
              "Connection error: #{inspect(other)}"
          end
        else
          "Connection error: #{inspect(inner_reason)}"
        end

      # Default case for simple errors
      true ->
        "Connection error: #{inspect(reason)}"
    end
  end

  # Helper function to find the indices of a connection in the connection sets
  defp find_connection_indices(connection_sets, client_id) do
    # Find the set index
    set_index =
      Enum.find_index(connection_sets, fn set ->
        Enum.any?(set.connections, fn conn -> conn.client_id == client_id end)
      end)

    if set_index != nil do
      # Find the connection index within the set
      set = Enum.at(connection_sets, set_index)

      connection_index =
        Enum.find_index(set.connections, fn conn -> conn.client_id == client_id end)

      {set_index, connection_index}
    else
      {nil, nil}
    end
  end

  # Helper function to remove a topic from connection state
  defp remove_topic_from_connection_state(
         connection_sets,
         set_index,
         connection_index,
         connection,
         topic
       ) do
    # Update the connection's topics list
    current_topics = connection.topics || []

    # Remove the topic from the list (always using new format - list of maps)
    updated_topics =
      Enum.reject(current_topics, fn t ->
        case t do
          %{topic: topic_str} -> topic_str == topic
          _ -> false
        end
      end)

    updated_connection = Map.put(connection, :topics, updated_topics)

    # Update the connection in the connection set
    set = Enum.at(connection_sets, set_index)
    updated_connections = List.replace_at(set.connections, connection_index, updated_connection)
    updated_set = Map.put(set, :connections, updated_connections)

    # Update the connection set in the connection sets list
    updated_connection_sets = List.replace_at(connection_sets, set_index, updated_set)

    success_message = "Unsubscribed from topic: #{topic}"
    {updated_connection_sets, success_message}
  end

  # Private function to handle broker tab closing
  defp handle_close_broker_tab(socket, name) do
    # Find the broker to close
    broker_to_close =
      ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, name)

    if broker_to_close do
      # Disconnect all connections in this broker before closing
      if Map.get(broker_to_close, :connections) do
        Enum.each(broker_to_close.connections, fn connection ->
          if connection.client_id do
            MqttClientManager.disconnect(connection.client_id)
          end
        end)
      end

      # Remove the trace table for this broker
      Mqttable.TraceManager.remove_broker(name)

      # Clean all UI state data related to this broker
      ConnectionSets.clean_broker_ui_state(name)

      # Remove the broker from the list
      updated_connection_sets =
        Enum.reject(socket.assigns.connection_sets, fn set ->
          set.name == name
        end)

      # Update the connection sets
      ConnectionSets.update(updated_connection_sets)

      # Select appropriate active broker using our helper function
      current_active_name =
        if socket.assigns.active_connection_set,
          do: socket.assigns.active_connection_set.name,
          else: nil

      new_active_set =
        if current_active_name == name do
          # Deleted broker was active, select a replacement
          ConnectionHelpers.select_active_broker(updated_connection_sets)
        else
          # Keep current active broker if it still exists
          ConnectionHelpers.select_active_broker(updated_connection_sets, current_active_name)
        end

      # Update UI state with new active broker
      new_active_name = if new_active_set, do: new_active_set.name, else: nil
      ConnectionSets.update_active_connection_set(new_active_name)

      # Load trace messages for the new active broker
      trace_messages =
        if new_active_name do
          Mqttable.TraceManager.get_messages(new_active_name)
        else
          []
        end

      socket =
        socket
        |> assign(:connection_sets, updated_connection_sets)
        |> assign(:active_connection_set, new_active_set)
        |> stream(:trace_messages, trace_messages)
        |> put_flash(:info, "Broker '#{name}' closed successfully")

      {:noreply, socket}
    else
      {:noreply, put_flash(socket, :error, "Broker not found")}
    end
  end

  # Default form state for the send message modal
  defp default_publish_form do
    %{
      "client_id" => "",
      "topic" => "",
      "payload" => "",
      "qos" => 0,
      "retain" => false,
      "content_type" => "",
      "payload_format_indicator" => false,
      "message_expiry_interval" => 0,
      "topic_alias" => 0,
      "response_topic" => "",
      "correlation_data" => "",
      "user_properties" => []
    }
  end

  # Format publish result based on the type of response
  defp format_publish_result(packet_info) do
    case packet_info do
      # QoS 0 messages return simple integer packet_id (usually 0)
      packet_id when is_integer(packet_id) ->
        {"Message sent successfully (Packet ID: #{packet_id})", :info}

      # QoS 1/2 messages return a map with detailed information
      %{packet_id: packet_id, reason_code: reason_code, reason_code_name: reason_name} ->
        case reason_code do
          0 ->
            # Success
            {"Message sent successfully (Packet ID: #{packet_id})", :info}

          # Error codes that indicate failure
          code
          when code in [
                 16,
                 17,
                 18,
                 19,
                 128,
                 129,
                 130,
                 131,
                 132,
                 133,
                 134,
                 135,
                 136,
                 137,
                 138,
                 139,
                 140,
                 141,
                 142,
                 143
               ] ->
            # 16: No matching subscribers
            # 17: No subscription existed
            # 18: Unspecified error
            # 19: Implementation specific error
            # 128+: Protocol errors, quota exceeded, etc.
            reason_text = format_reason_code_name(reason_name)
            {"Message delivery failed: #{reason_text} (Code: #{code})", :error}

          # Warning codes (informational but not necessarily errors)
          code when code in [1, 2, 3, 4] ->
            # Informational codes
            reason_text = format_reason_code_name(reason_name)
            {"Message sent with notice: #{reason_text} (Code: #{code})", :warning}

          code ->
            # Other unknown codes
            reason_text = format_reason_code_name(reason_name)
            {"Message sent with unknown status: #{reason_text} (Code: #{code})", :warning}
        end

      # Fallback for unexpected format
      other ->
        {"Message sent (Response: #{inspect(other)})", :info}
    end
  end

  # Format reason code name for display
  defp format_reason_code_name(reason_name) when is_atom(reason_name) do
    reason_name
    |> Atom.to_string()
    |> String.replace("_", " ")
    |> String.capitalize()
  end

  defp format_reason_code_name(other), do: inspect(other)

  # Helper functions for scheduled messages

  # Find connection and scheduled message by client_id and index
  defp find_connection_and_scheduled_message(connection_sets, client_id, index) do
    Enum.find_value(connection_sets, fn connection_set ->
      case Enum.find(connection_set.connections, fn conn -> conn.client_id == client_id end) do
        nil ->
          nil

        connection ->
          scheduled_messages = Map.get(connection, :scheduled_messages, [])

          if index >= 0 && index < length(scheduled_messages) do
            scheduled_message = Enum.at(scheduled_messages, index)
            {connection_set, connection, scheduled_message}
          else
            nil
          end
      end
    end)
  end

  # Add scheduled message to connection sets
  defp add_scheduled_message_to_sets(connection_sets, client_id, scheduled_message) do
    Enum.map(connection_sets, fn connection_set ->
      updated_connections =
        Enum.map(connection_set.connections, fn connection ->
          if connection.client_id == client_id do
            current_scheduled_messages = Map.get(connection, :scheduled_messages, [])
            updated_scheduled_messages = current_scheduled_messages ++ [scheduled_message]
            Map.put(connection, :scheduled_messages, updated_scheduled_messages)
          else
            connection
          end
        end)

      Map.put(connection_set, :connections, updated_connections)
    end)
  end

  # Update scheduled message in connection sets
  defp update_scheduled_message_in_sets(connection_sets, client_id, index, scheduled_message) do
    Enum.map(connection_sets, fn connection_set ->
      updated_connections =
        Enum.map(connection_set.connections, fn connection ->
          if connection.client_id == client_id do
            current_scheduled_messages = Map.get(connection, :scheduled_messages, [])

            if index >= 0 && index < length(current_scheduled_messages) do
              updated_scheduled_messages =
                List.replace_at(current_scheduled_messages, index, scheduled_message)

              Map.put(connection, :scheduled_messages, updated_scheduled_messages)
            else
              connection
            end
          else
            connection
          end
        end)

      Map.put(connection_set, :connections, updated_connections)
    end)
  end

  # Remove scheduled message from connection sets
  defp remove_scheduled_message_from_sets(connection_sets, client_id, index) do
    Enum.map(connection_sets, fn connection_set ->
      updated_connections =
        Enum.map(connection_set.connections, fn connection ->
          if connection.client_id == client_id do
            current_scheduled_messages = Map.get(connection, :scheduled_messages, [])

            if index >= 0 && index < length(current_scheduled_messages) do
              updated_scheduled_messages = List.delete_at(current_scheduled_messages, index)
              Map.put(connection, :scheduled_messages, updated_scheduled_messages)
            else
              connection
            end
          else
            connection
          end
        end)

      Map.put(connection_set, :connections, updated_connections)
    end)
  end

  # Find connection by client_id across all connection sets
  defp find_connection_by_client_id(connection_sets, client_id) do
    Enum.find_value(connection_sets, fn connection_set ->
      Enum.find(connection_set.connections, fn connection ->
        connection.client_id == client_id
      end)
    end)
  end

  # Synchronize scheduled messages with the worker
  defp sync_client_scheduled_messages(socket, client_id) do
    # Find the connection and get its scheduled messages
    connection = find_connection_by_client_id(socket.assigns.connection_sets, client_id)
    scheduled_messages = Map.get(connection, :scheduled_messages, [])

    # Sync with the worker
    Mqttable.MqttClient.Worker.sync_scheduled_messages(client_id, scheduled_messages)
  end

  # File upload validation functions
  defp validate_file_upload(file_info) do
    with {:ok, _} <- validate_file_size(file_info.size),
         {:ok, _} <- validate_file_type(file_info.type),
         {:ok, _} <- validate_file_name(file_info.name),
         {:ok, _} <- validate_base64_content(file_info.content) do
      {:ok, file_info}
    else
      {:error, reason} -> {:error, reason}
    end
  end

  # Maximum file size for MQTT (16MB as base64 will be ~33% larger)
  @max_file_size 16 * 1024 * 1024

  defp validate_file_size(size) when is_integer(size) and size > 0 and size <= @max_file_size do
    {:ok, size}
  end

  defp validate_file_size(size) when is_integer(size) and size > @max_file_size do
    {:error,
     "File size (#{format_file_size(size)}) exceeds maximum limit of #{format_file_size(@max_file_size)}"}
  end

  defp validate_file_size(_) do
    {:error, "Invalid file size"}
  end

  defp validate_file_type(type) when is_binary(type) and byte_size(type) > 0 do
    {:ok, type}
  end

  defp validate_file_type(_) do
    {:error, "Invalid file type"}
  end

  defp validate_file_name(name) when is_binary(name) and byte_size(name) > 0 do
    # Basic filename validation
    if String.length(name) > 255 do
      {:error, "Filename too long (maximum 255 characters)"}
    else
      {:ok, name}
    end
  end

  defp validate_file_name(_) do
    {:error, "Invalid filename"}
  end

  defp validate_base64_content(content) when is_binary(content) do
    # Validate that content is valid base64
    case Base.decode64(content) do
      {:ok, _decoded} -> {:ok, content}
      :error -> {:error, "Invalid file content encoding"}
    end
  end

  defp validate_base64_content(_) do
    {:error, "Invalid file content"}
  end

  defp format_file_size(size) when is_integer(size) and size >= 0 do
    cond do
      size >= 1_073_741_824 -> "#{Float.round(size / 1_073_741_824, 2)} GB"
      size >= 1_048_576 -> "#{Float.round(size / 1_048_576, 2)} MB"
      size >= 1024 -> "#{Float.round(size / 1024, 2)} KB"
      true -> "#{size} bytes"
    end
  end

  defp format_file_size(_), do: "Unknown size"
end
